import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import '../../../../domain/models/top_performers.dart';
import '../../../cubit/top_performers/top_performers_cubit.dart';
import '../../../shared/components/calendar/month_year_picker.dart';
import '/src/core/enum/user_role.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../domain/models/user.dart';
import '../../../cubit/user/user_cubit.dart';
import 'sales_by_brokers_card.dart';

// Global ValueNotifier to persist selected month across widget recreations
final ValueNotifier<String> _globalSelectedMonth = ValueNotifier<String>(
  DateFormat('yyyy-MM').format(DateTime.now()),
);

class CommissionCard extends HookWidget {
  const CommissionCard({super.key});

  @override
  Widget build(BuildContext context) {
    final showBrokers = useState<bool>(true);
    final topTenCommissionedBrokers = useState<List<TopPerformers>>([]);
    final topTenCommissionedAgents = useState<List<TopPerformers>>([]);
    final topTenSalesBrokers = useState<List<TopPerformers>>([]);
    final topTenSalesAgents = useState<List<TopPerformers>>([]);
    final user = context.watch<UserCubit>().state.user;
    final userRole = user?.role ?? "";
    final topPerformersCubit = context.read<TopPerformersCubit>();

    // Use the global ValueNotifier directly to persist month across widget recreations
    final selectMonth = _globalSelectedMonth;
    final showCalendar = useState<bool>(false);
    final buttonKey = useState<GlobalKey>(GlobalKey());

    print(
      "🔵 WIDGET BUILD - selectMonth.value: '${selectMonth.value}', global: '${_globalSelectedMonth.value}', same object: ${identical(selectMonth, _globalSelectedMonth)}",
    );

    // Fetch data when month or user role changes
    useEffect(() {
      print(
        "🟢 DATA useEffect - selectMonth.value: '${selectMonth.value}', userRole: '$userRole'",
      );
      if (selectMonth.value.isNotEmpty) {
        Future.microtask(() async {
          print("🟢 DATA - Fetching data for month: ${selectMonth.value}");
          if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
            await topPerformersCubit.getAgentTopPerformers(selectMonth.value);
          } else if (userRole == UserRole.admin ||
              userRole == UserRole.platformOwner) {
            await topPerformersCubit.getBrokerageTopPerformers(
              selectMonth.value,
            );
            await topPerformersCubit.getAgentTopPerformers(selectMonth.value);
          }
        });
      } else {
        print("🟢 DATA - Month is empty, skipping data fetch");
      }
      return null;
    }, [selectMonth.value, userRole]);

    final Size size = MediaQuery.of(context).size;
    print(
      "🔴 WIDGET BUILD - Screen size: ${size.width}x${size.height}, selectMonth: '${selectMonth.value}'",
    );
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commissionCardBreakPoint;

    // Calculate the right offset for the calendar dynamically
    final cardWidth = Responsive.isDesktop(context)
        ? size.width * 0.2
        : size.width;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Commission card (main content)
        LayoutBuilder(
          builder: (context, constraints) {
            return BlocConsumer<TopPerformersCubit, TopPerformersState>(
              listener: (context, state) {
                if (state is TopPerformersError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error: ${state.message}')),
                  );
                }

                if (state is TopPerformersLoaded) {
                  if (state.agentTopPerformers != null) {
                    topTenCommissionedAgents.value = [
                      ...state.agentTopPerformers!,
                    ];

                    final sortedAgents = [...state.agentTopPerformers!];
                    sortedAgents.sort(
                      (a, b) =>
                          b.monthlySalesCount.compareTo(a.monthlySalesCount),
                    );

                    topTenSalesAgents.value = sortedAgents;
                  }

                  if (state.brokerageTopPerformers != null) {
                    topTenCommissionedBrokers.value = [
                      ...state.brokerageTopPerformers!,
                    ];

                    final sortedBrokers = [...state.brokerageTopPerformers!];
                    sortedBrokers.sort(
                      (a, b) =>
                          b.monthlySalesCount.compareTo(a.monthlySalesCount),
                    );

                    topTenSalesBrokers.value = sortedBrokers;
                  }
                }
              },
              builder: (context, state) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Container(
                    constraints: BoxConstraints(maxHeight: size.height),
                    width: cardWidth,
                    decoration: _buildBoxDecoration(),
                    child: Responsive.isTablet(context)
                        ? Container(
                            height: size.height / 1.6,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      _buildHeader(
                                        isSmallView,
                                        size,
                                        selectMonth,
                                        topPerformersCubit,
                                        context,
                                        showCalendar,
                                        buttonKey,
                                      ),
                                      _buildTabRow(size, showBrokers, user),
                                      const SizedBox(height: defaultPadding),
                                      _buildPerformersList(
                                        isSmallView,
                                        user?.role == UserRole.brokerage ||
                                                user?.role ==
                                                    UserRole.officeStaff
                                            ? true
                                            : user?.role == UserRole.agent
                                            ? false
                                            : showBrokers.value,
                                        topTenCommissionedBrokers,
                                        topTenCommissionedAgents,
                                        user,
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: ValueListenableBuilder(
                                    valueListenable: showBrokers,
                                    builder: (context, value, child) {
                                      return SalesByBrokersCard(
                                        brokersList:
                                            topTenCommissionedBrokers.value,
                                        agentsList:
                                            topTenCommissionedAgents.value,
                                        isBrokerView: showBrokers.value,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildHeader(
                                isSmallView,
                                size,
                                selectMonth,
                                topPerformersCubit,
                                context,
                                showCalendar,
                                buttonKey,
                              ),
                              _buildTabRow(size, showBrokers, user),
                              const SizedBox(height: defaultPadding),
                              _buildPerformersList(
                                isSmallView,
                                user?.role == UserRole.brokerage ||
                                        user?.role == UserRole.officeStaff ||
                                        user?.role == UserRole.agent
                                    ? false
                                    : showBrokers.value,
                                topTenCommissionedBrokers,
                                topTenCommissionedAgents,
                                user,
                              ),
                              const SizedBox(height: defaultPadding),
                              Flexible(
                                child: SalesByBrokersCard(
                                  isBrokerView:
                                      user?.role == UserRole.brokerage ||
                                          user?.role == UserRole.officeStaff ||
                                          user?.role == UserRole.agent
                                      ? false
                                      : showBrokers.value,
                                  brokersList: topTenSalesBrokers.value,
                                  agentsList: topTenSalesAgents.value,
                                ),
                              ),
                              const SizedBox(height: defaultPadding / 1.5),
                            ],
                          ),
                  ),
                );
              },
            );
          },
        ),
        // Calendar overlay - positioned relative to button
        if (showCalendar.value)
          _buildCalendarOverlay(
            buttonKey,
            size,
            selectMonth,
            showCalendar,
            topPerformersCubit,
            context,
          ),
      ],
    );
  }

  Widget _buildCalendarOverlay(
    ValueNotifier<GlobalKey> buttonKey,
    Size screenSize,
    ValueNotifier<String> selectMonth,
    ValueNotifier<bool> showCalendar,
    TopPerformersCubit topPerformersCubit,
    BuildContext context,
  ) {
    return ValueListenableBuilder<GlobalKey>(
      valueListenable: buttonKey,
      builder: (context, key, child) {
        return LayoutBuilder(
          builder: (context, constraints) {
            // Get button position and size
            final RenderBox? buttonBox =
                key.currentContext?.findRenderObject() as RenderBox?;

            // Calendar dimensions
            const calendarWidth = 280.0;
            const calendarHeight = 350.0;
            const padding = 8.0; // Horizontal padding from card's right edge
            const topPadding =
                48.0; // Increased top padding to clear header and mimic dropdown

            // Calculate commission card's position and size
            final cardWidth = Responsive.isDesktop(context)
                ? screenSize.width * 0.2
                : screenSize.width;
            final cardPosition =
                Offset.zero; // Assume card is at (0,0) in the Stack
            final cardRightEdge =
                cardPosition.dx + cardWidth; // Right edge of the card

            // Position calendar to the right of the commission card
            double calendarLeft = cardRightEdge + padding;
            double calendarTop = topPadding; // Start with fixed top padding

            // Align calendar below the button for dropdown effect
            if (buttonBox != null) {
              final buttonPosition = buttonBox.localToGlobal(Offset.zero);
              final buttonHeight = buttonBox.size.height;
              calendarTop =
                  buttonPosition.dy +
                  buttonHeight +
                  padding; // Position below button
            }

            // Ensure calendar stays within screen bounds
            const screenPadding = 16.0;
            final maxTop = screenSize.height - calendarHeight - screenPadding;
            final maxLeft = screenSize.width - calendarWidth - screenPadding;
            final minLeft = screenPadding;

            // Adjust horizontal position
            if (calendarLeft > maxLeft) {
              // If it exceeds right edge, align to left edge of button or card
              calendarLeft = buttonBox != null
                  ? buttonBox.localToGlobal(Offset.zero).dx -
                        calendarWidth -
                        padding
                  : cardPosition.dx - calendarWidth - padding;
              if (calendarLeft < minLeft) {
                calendarLeft = minLeft;
              }
            }

            // Adjust vertical position
            if (calendarTop > maxTop) {
              // If it exceeds bottom, align above the button
              calendarTop = buttonBox != null
                  ? buttonBox.localToGlobal(Offset.zero).dy -
                        calendarHeight -
                        padding
                  : topPadding;
              if (calendarTop < screenPadding) {
                calendarTop = screenPadding;
              }
            }

            return Positioned(
              top: calendarTop,
              left: calendarLeft,
              child: _buildCalendarWidget(
                context,
                selectMonth,
                showCalendar,
                topPerformersCubit,
                buttonKey,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCalendarWidget(
    BuildContext context,
    ValueNotifier<String> selectMonth,
    ValueNotifier<bool> showCalendar,
    TopPerformersCubit topPerformersCubit,
    ValueNotifier<GlobalKey> buttonKey,
  ) {
    return Material(
      elevation: 8.0,
      borderRadius: BorderRadius.circular(12.0),
      clipBehavior: Clip.antiAlias,
      color: Colors.white,
      child: Container(
        width: 280.0,
        height: 350.0,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: Colors.grey.shade300, width: 1),
        ),
        child: MonthYearPicker(
          initialDate: DateTime.parse('${selectMonth.value}-01'),
          anchorKey: buttonKey.value,
          onDateSelected: (DateTime date) async {
            await _handleDateSelection(
              date: date,
              selectMonth: selectMonth,
              topPerformersCubit: topPerformersCubit,
              context: context,
            );
            showCalendar.value = false;
          },
          onCancel: () {
            showCalendar.value = false;
          },
        ),
      ),
    );
  }

  Future<void> _handleDateSelection({
    required DateTime date,
    required ValueNotifier<String> selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext context,
  }) async {
    final formattedDate = DateFormat('yyyy-MM').format(date);
    selectMonth.value = formattedDate;

    if (!context.mounted) return;

    final userRole = context.read<UserCubit>().state.user?.role;

    if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    } else if (userRole == UserRole.admin ||
        userRole == UserRole.platformOwner) {
      await topPerformersCubit.getBrokerageTopPerformers(formattedDate);
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    }
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.commissionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildHeader(
    bool isSmallView,
    Size size,
    ValueNotifier<String> selectMonth,
    TopPerformersCubit topPerformersCubit,
    BuildContext context,
    ValueNotifier<bool> showCalendar,
    ValueNotifier<GlobalKey> buttonKey,
  ) {
    final bool isMobile = Responsive.isMobile(context);
    final bool isTablet = Responsive.isTablet(context);

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: defaultPadding,
        horizontal: defaultPadding,
      ),
      color: AppTheme.commissionCardDarkColor,
      child: isMobile || isTablet
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  topPerformers,
                  style: AppFonts.semiBoldTextStyle(
                    isMobile ? 16 : 18,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: defaultPadding / 2),
                Align(
                  alignment: Alignment.centerRight,
                  child: _buildMonthSelection(
                    size,
                    selectMonth,
                    topPerformersCubit,
                    context,
                    showCalendar,
                    buttonKey,
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  topPerformers,
                  style: AppFonts.semiBoldTextStyle(
                    size.width < 1330 && size.width > 1200 ? 16 : 18,
                    color: Colors.white,
                  ),
                ),
                _buildMonthSelection(
                  size,
                  selectMonth,
                  topPerformersCubit,
                  context,
                  showCalendar,
                  buttonKey,
                ),
              ],
            ),
    );
  }

  Widget _buildMonthSelection(
    Size size,
    ValueNotifier<String> selectMonth,
    TopPerformersCubit topPerformersCubit,
    BuildContext context,
    ValueNotifier<bool> showCalendar,
    ValueNotifier<GlobalKey> buttonKey,
  ) {
    final bool isMobile = Responsive.isMobile(context);
    bool restrictSize = size.width >= 1200 && size.width < 1330;

    return Material(
      key: buttonKey.value,
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          showCalendar.value = !showCalendar.value;
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isMobile ? 8 : (restrictSize ? 5 : 12),
            vertical: isMobile ? 8 : 5,
          ),
          decoration: BoxDecoration(
            color: AppTheme.commissionDropDownBgColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                DateFormat(
                  'MMM yyyy',
                ).format(DateTime.parse('${selectMonth.value}-01')),
                style: AppFonts.mediumTextStyle(
                  isMobile
                      ? 12
                      : (size.width >= 1100 && size.width < 1570 ? 11 : 14),
                  color: Colors.white,
                ),
              ),
              SizedBox(
                width: isMobile
                    ? 4
                    : (size.width < 1570 && size.width >= 1100 ? 2 : 4),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: isMobile ? 14 : (restrictSize ? 10 : 16),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabRow(Size size, ValueNotifier<bool> showBrokers, User? user) {
    return user?.role == UserRole.admin || user?.role == UserRole.platformOwner
        ? Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(
              defaultPadding,
              0,
              defaultPadding,
              defaultPadding,
            ),
            decoration: BoxDecoration(color: AppTheme.commissionCardDarkColor),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.commissionCardColor,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTab(brokerLabel, showBrokers.value, () {
                        showBrokers.value = true;
                      }),
                      _buildTab(agentLabel, !showBrokers.value, () {
                        showBrokers.value = false;
                      }),
                    ],
                  ),
                ),
              ),
            ),
          )
        : SizedBox.shrink();
  }

  Widget _buildTab(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.commissionDropDownBgColor
              : AppTheme.commissionCardColor,
          borderRadius: isSelected
              ? BorderRadius.circular(20)
              : BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
        ),
        child: Text(
          text,
          style: AppFonts.mediumTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildPerformersList(
    bool isSmallView,
    bool showBrokers,
    ValueNotifier<List<TopPerformers>> topTenBrokers,
    ValueNotifier<List<TopPerformers>> topTenAgents,
    User? user,
  ) {
    return Expanded(
      child: Container(
        color: AppTheme.commissionCardColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              commissionTab,
              textAlign: TextAlign.center,
              style: AppFonts.semiBoldTextStyle(16, color: Colors.white),
            ),
            const SizedBox(height: defaultPadding),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
                itemCount: showBrokers
                    ? topTenBrokers.value.length
                    : topTenAgents.value.length,
                itemBuilder: (context, index) {
                  final bgColor = index % 2 == 0
                      ? AppTheme.commissionCardColor
                      : AppTheme.commissionCardDarkColor;
                  if (showBrokers) {
                    final broker = topTenBrokers.value[index];
                    return _buildBrokerPerformerItem(
                      broker,
                      isSmallView,
                      bgColor,
                    );
                  } else {
                    final agent = topTenAgents.value[index];
                    return _buildAgentPerformerItem(
                      agent,
                      isSmallView,
                      bgColor,
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBrokerPerformerItem(
    TopPerformers broker,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(
              broker.name,
              broker.downlineAgentsCount,
              false,
              '',
            ),
            _earningsWidget(broker.monthlyRevenue),
          ],
        ),
      ),
    );
  }

  Widget _userAvatar() {
    return CircleAvatar(
      radius: 18,
      backgroundColor: Colors.white.withValues(alpha: 0.2),
      child: Icon(Icons.person, color: Colors.white, size: 18),
    );
  }

  Widget _buildAgentPerformerItem(
    TopPerformers agent,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(
              agent.name,
              agent.downlineAgentsCount,
              true,
              agent.associatedBroker,
            ),
            SizedBox(width: 12),
            _earningsWidget(agent.monthlyRevenue),
          ],
        ),
      ),
    );
  }

  Widget _nameSalesCountWidget(
    String name,
    int agentsCount,
    bool isAgent,
    String relatedBroker,
  ) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.semiBoldTextStyle(13, color: Colors.white),
          ),
          SizedBox(height: 3),
          Text(
            '$agentsCount $agentLabel',
            style: AppFonts.regularTextStyle(
              11,
              color: AppTheme.commissionSalesTextColor,
            ),
          ),

          if (isAgent) ...[
            Divider(color: AppTheme.commissionSalesTextColor),
            Text(
              relatedBrokerLabel,
              style: AppFonts.regularTextStyle(
                11,
                color: AppTheme.commissionSalesTextColor,
              ),
            ),
            SizedBox(height: 3),
            Text(
              relatedBroker,
              style: AppFonts.semiBoldTextStyle(13, color: AppTheme.white),
            ),
          ],
        ],
      ),
    );
  }

  Widget _earningsWidget(double totalCommission) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '\$${totalCommission.toInt()}',
          style: AppFonts.semiBoldTextStyle(14, color: Colors.white),
        ),
        SizedBox(height: 3),
        Text(
          revenueEarnedLabel,
          maxLines: 2,
          style: AppFonts.mediumTextStyle(
            10,
            color: AppTheme.commissionSalesTextColor,
          ),
        ),
      ],
    );
  }
}